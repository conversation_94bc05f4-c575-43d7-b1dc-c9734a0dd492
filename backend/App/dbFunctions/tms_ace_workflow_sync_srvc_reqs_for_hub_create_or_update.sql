CREATE OR REPLACE FUNCTION public.tms_ace_workflow_sync_srvc_reqs_for_hub_create_or_update(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean;
    message text;
    code text;
    affected_rows integer;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    vertical_id_ integer;
    hub_id_ bigint;
    pincodes_ text[];
    is_active_ boolean;

    -- For processing
    srvc_req_ids_to_untag integer[];
    srvc_req_ids_to_tag integer[];
    user_context_json json;
    hub_update_json json;
    hub_title_ text;
    total_updated_count integer := 0;

begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    vertical_id_ := (form_data_->>'vertical_id')::integer;
    hub_id_ := (form_data_->>'hub_id')::bigint;
    pincodes_ := array(select json_array_elements_text(form_data_->'pincodes'));
    is_active_ := (form_data_->>'is_active')::boolean;

    -- Get user context for updates
    user_context_json = tms_get_user_context_from_data(form_data_);

    -- Get hub title
    select hub_name
      from public.cl_tx_vertical_srvc_hubs
     where id = hub_id_
       and org_id = org_id_
      into hub_title_;

    if hub_title_ is null then
        status = false;
        message = 'hub_not_found';
        resp_data = '{}';
        return json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
    end if;

    -- Step 1: Get all service requests that are currently tagged to this hub
    -- but the pincode in it does not match the hub's pincodes or hub is deactivated
    select array_agg(db_id)
      from cl_tx_srvc_req
     where srvc_prvdr = org_id_
       and prvdr_srvc_hub = hub_id_
       and is_deleted is not true
       and (
           -- Hub is deactivated OR pincode doesn't match hub's pincodes
           is_active_ is not true
           OR cust_pincode != all(pincodes_)
       )
      into srvc_req_ids_to_untag;

    -- Step 2: Get all service requests that fall under this vertical
    -- and have pincode matching this hub but are not currently tagged to this hub
    -- (only if hub is active)
    if is_active_ is true then
        select array_agg(db_id)
          from cl_tx_srvc_req
         where srvc_prvdr = org_id_
           and prvdr_vertical = vertical_id_
           and (prvdr_srvc_hub is null or prvdr_srvc_hub = 0 or prvdr_srvc_hub != hub_id_)
           and cust_pincode = any(pincodes_)
           and cust_pincode is not null
           and cust_pincode != ''
           and is_deleted is not true
          into srvc_req_ids_to_tag;
    end if;

    -- Process untagging (remove hub assignment)
    if srvc_req_ids_to_untag is not null and array_length(srvc_req_ids_to_untag, 1) > 0 then
        -- Prepare the update JSON to remove hub assignment
        hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(0::bigint),true);
        hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(''::text),true);

        -- Remove hub assignment from all filtered service requests
        FOR i IN 1..array_length(srvc_req_ids_to_untag, 1) LOOP
            -- Get the srvc_type_id for this service request and add it to the update JSON
            declare
                current_srvc_type_id integer;
                current_hub_update_json json;
            begin
                select srvc_type_id
                  from cl_tx_srvc_req
                 where db_id = srvc_req_ids_to_untag[i]
                  into current_srvc_type_id;

                current_hub_update_json = jsonb_set(hub_update_json::jsonb,'{srvc_type_id}',to_jsonb(current_srvc_type_id),true);
                perform tms_create_service_request(current_hub_update_json, srvc_req_ids_to_untag[i]);
            end;
        END LOOP;

        total_updated_count := total_updated_count + array_length(srvc_req_ids_to_untag, 1);
    end if;

    -- Process tagging (assign hub to matching service requests)
    if srvc_req_ids_to_tag is not null and array_length(srvc_req_ids_to_tag, 1) > 0 then
        -- Prepare the update JSON to assign hub
        hub_update_json = jsonb_set(user_context_json::jsonb,'{prvdr_srvc_hub}',to_jsonb(hub_id_),true);
        hub_update_json = jsonb_set(hub_update_json::jsonb,'{prvdr_srvc_hub_title}',to_jsonb(hub_title_),true);

        -- Assign hub to all matching service requests
        FOR i IN 1..array_length(srvc_req_ids_to_tag, 1) LOOP
            -- Get the srvc_type_id for this service request and add it to the update JSON
            declare
                current_srvc_type_id integer;
                current_hub_update_json json;
            begin
                select srvc_type_id
                  from cl_tx_srvc_req
                 where db_id = srvc_req_ids_to_tag[i]
                  into current_srvc_type_id;

                current_hub_update_json = jsonb_set(hub_update_json::jsonb,'{srvc_type_id}',to_jsonb(current_srvc_type_id),true);
                perform tms_create_service_request(current_hub_update_json, srvc_req_ids_to_tag[i]);
            end;
        END LOOP;

        total_updated_count := total_updated_count + array_length(srvc_req_ids_to_tag, 1);
    end if;

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'total_updated_count', total_updated_count,
        'untagged_count', coalesce(array_length(srvc_req_ids_to_untag, 1), 0),
        'tagged_count', coalesce(array_length(srvc_req_ids_to_tag, 1), 0),
        'untagged_srvc_req_ids', coalesce(srvc_req_ids_to_untag, array[]::integer[]),
        'tagged_srvc_req_ids', coalesce(srvc_req_ids_to_tag, array[]::integer[]),
        'hub_id', hub_id_,
        'hub_title', hub_title_,
        'is_active', is_active_
    );

    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );
end;
$function$;
